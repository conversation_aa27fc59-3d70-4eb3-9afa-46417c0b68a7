import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth-utils';
import { spawn } from 'child_process';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { batchSize = 10, qualityThreshold = 70, skipRecent = true } = body;

    // Validate settings
    if (batchSize < 1 || batchSize > 50) {
      return NextResponse.json(
        { error: 'Batch size must be between 1 and 50' },
        { status: 400 }
      );
    }

    if (qualityThreshold < 0 || qualityThreshold > 100) {
      return NextResponse.json(
        { error: 'Quality threshold must be between 0 and 100' },
        { status: 400 }
      );
    }

    // Check if there's already a running job
    // In production, this would check a job queue like <PERSON> or similar
    const runningJob = await checkForRunningJob();
    if (runningJob) {
      return NextResponse.json(
        { error: 'Enhancement job is already running' },
        { status: 409 }
      );
    }

    // Start the enhancement script as a background process
    const scriptPath = path.join(process.cwd(), 'scripts', 'enhance-existing-products.ts');
    const args = [
      `--batch-size=${batchSize}`,
      skipRecent ? '--skip-recent' : '--force'
    ];

    // In production, you'd use a proper job queue
    // For now, we'll simulate starting the job
    const jobId = `enhancement_${Date.now()}`;
    
    try {
      // Spawn the enhancement script
      const child = spawn('tsx', [scriptPath, ...args], {
        detached: true,
        stdio: 'ignore'
      });
      
      child.unref(); // Allow the parent process to exit independently
      
      // Store job info (in production, use Redis or database)
      await storeJobInfo(jobId, {
        status: 'running',
        startedAt: new Date(),
        settings: { batchSize, qualityThreshold, skipRecent },
        triggeredBy: user.id
      });

      return NextResponse.json({
        success: true,
        job: {
          id: jobId,
          status: 'running',
          progress: 0,
          startedAt: new Date().toISOString(),
          settings: { batchSize, qualityThreshold, skipRecent }
        }
      });

    } catch (error) {
      console.error('Failed to start enhancement script:', error);
      return NextResponse.json(
        { error: 'Failed to start enhancement process' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error starting bulk enhancement:', error);
    return NextResponse.json(
      { error: 'Failed to start bulk enhancement' },
      { status: 500 }
    );
  }
}

// Helper function to check for running jobs
async function checkForRunningJob(): Promise<boolean> {
  // In production, this would check a job queue or Redis
  // For now, we'll check the database for recent processing entries
  try {
    const { prisma } = await import('@/lib/prisma');
    
    const recentProcessing = await prisma.imageEnhancementLog.findFirst({
      where: {
        status: 'processing',
        createdAt: {
          gte: new Date(Date.now() - 60 * 60 * 1000) // Last hour
        }
      }
    });

    return !!recentProcessing;
  } catch (error) {
    console.error('Error checking for running jobs:', error);
    return false;
  }
}

// Helper function to store job information
async function storeJobInfo(jobId: string, jobData: any): Promise<void> {
  // In production, this would store in Redis or a job queue
  // For now, we'll store in the database
  try {
    const { prisma } = await import('@/lib/prisma');
    
    // Create a log entry to track the bulk job
    await prisma.imageEnhancementLog.create({
      data: {
        id: jobId,
        productId: 'BULK_JOB', // Special identifier for bulk jobs
        originalImageUrl: 'bulk_enhancement',
        enhancementType: 'bulk_processing',
        status: 'processing',
        triggeredBy: jobData.triggeredBy
      }
    });
  } catch (error) {
    console.error('Error storing job info:', error);
  }
}
