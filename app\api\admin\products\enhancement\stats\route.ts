import { NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth-utils';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    // Check authentication and admin role
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    // Get enhancement statistics
    const [
      totalProducts,
      enhancedProducts,
      pendingProducts,
      failedProducts,
      qualityStats,
      recentJobs
    ] = await Promise.all([
      // Total active products with images
      prisma.product.count({
        where: {
          isActive: true,
          images: { isEmpty: false }
        }
      }),
      
      // Enhanced products
      prisma.product.count({
        where: {
          isActive: true,
          enhancementStatus: 'completed',
          images: { isEmpty: false }
        }
      }),
      
      // Pending enhancement
      prisma.product.count({
        where: {
          isActive: true,
          OR: [
            { enhancementStatus: null },
            { enhancementStatus: 'pending' }
          ],
          images: { isEmpty: false }
        }
      }),
      
      // Failed enhancements
      prisma.product.count({
        where: {
          isActive: true,
          enhancementStatus: 'failed',
          images: { isEmpty: false }
        }
      }),
      
      // Average quality score
      prisma.product.aggregate({
        where: {
          isActive: true,
          enhancementStatus: 'completed',
          qualityScore: { not: null }
        },
        _avg: {
          qualityScore: true
        }
      }),
      
      // Recent enhancement jobs
      prisma.imageEnhancementLog.findMany({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
        select: {
          id: true,
          status: true,
          createdAt: true,
          completedAt: true,
          processingTime: true,
          qualityScore: true,
          enhancementType: true
        }
      })
    ]);

    // Check for currently running jobs (this would be stored in Redis or similar in production)
    // For now, we'll check for recent processing status
    const currentJob = await prisma.imageEnhancementLog.findFirst({
      where: {
        status: 'processing',
        createdAt: {
          gte: new Date(Date.now() - 60 * 60 * 1000) // Last hour
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    const stats = {
      total: totalProducts,
      enhanced: enhancedProducts,
      pending: pendingProducts,
      failed: failedProducts,
      averageQuality: qualityStats._avg.qualityScore || 0,
      lastRun: recentJobs[0]?.completedAt?.toISOString()
    };

    // Mock current job data (in production, this would come from a job queue)
    const jobData = currentJob ? {
      id: currentJob.id,
      status: 'running' as const,
      progress: 45, // This would be calculated based on actual progress
      startedAt: currentJob.createdAt.toISOString(),
      stats: {
        processed: 23, // These would be real-time stats
        enhanced: 18,
        failed: 2
      }
    } : null;

    return NextResponse.json({
      success: true,
      stats,
      currentJob: jobData,
      recentJobs: recentJobs.map(job => ({
        id: job.id,
        status: job.status,
        type: job.enhancementType,
        duration: job.processingTime,
        quality: job.qualityScore,
        completedAt: job.completedAt?.toISOString()
      }))
    });

  } catch (error) {
    console.error('Error fetching enhancement stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch enhancement statistics' },
      { status: 500 }
    );
  }
}
