"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  Star,
  ShoppingBag,
  Heart,
  CheckCircle,
  Box,
  BoxIcon,
  BoxesIcon,
  Sparkles,
  TrendingUp,
  Users,
  Award,
  Zap,
  Shield,
  Clock,
  Truck
} from "lucide-react";
import NavBar from "@/components/navbar";
import Footer from "@/components/footer";
import { useSession } from "@/lib/auth-client";
import { User } from "@/utils/types";
import { getUserById } from "@/actions/userActions";
import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import NavBarSkeleton from "@/components/navBarSkeleton";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";

// Replace the featuredProducts array with real Air Jordan products from the catalog
const featuredProducts = [
  {
    id: "cmcs5vhd10007jw04i5tftyvc",
    name: "Air Jordan 1 Low OG 'Pine Green'",
    price: "M 1,229.99",
    image: "https://utfs.io/f/9WixNlVtj4JyzBi0To7B9bPgSIj421mKLC7TfcXiRDJZYN3s",
    badge: "Trendy",
  },
  {
    id: "cmczartm60007l204uci3rj2q",
    name: "Air Jordan 1 Low OG Gym Red/Black",
    price: "M 1,299.98",
    image: "https://utfs.io/f/9WixNlVtj4Jyp53LzMdKSdtR1ofr36MIGswkUHvLlQOp0yP5",
    badge: "Trendy",
  },
  {
    id: "cmcs5vhh80013jw04cp89gguj",
    name: "Air Jordan 1 OG Denim",
    price: "M 1,370.00",
    image: "https://utfs.io/f/9WixNlVtj4JyR2z8Kfr75Nng6rhoIMJw0fvsEzWlY93auqiT",
    badge: "Trendy",
  },
  {
    id: "cmczartm50005l2040dhst1e2",
    name: "Air Jordan 1 Retro",
    price: "M 1,490.00",
    image: "https://utfs.io/f/9WixNlVtj4JyNUkyHXJgqYPJV2BRlX76uwHFvyt1mjW9Sc8o",
    badge: "Trendy",
  },
  {
    id: "cmcs0ldwz0003jx0480m8ekrt",
    name: "Air Jordan 4 (Light Blue/Black)",
    price: "M 1,349.98",
    image: "https://utfs.io/f/9WixNlVtj4JyAanVEbYcKdFpNsIe6iXMVxzShTf8U3YCPygH",
    badge: "Trendy",
  },
  {
    id: "cmcs0ldx90005jx04mq80syto",
    name: "Air Jordan 4 (Light Brown)",
    price: "M 1,350.00",
    image: "https://utfs.io/f/9WixNlVtj4JyWBfxipUhC8tH3luzcRNSwXj1BpqO7UFQoTEf",
    badge: "Trendy",
  },
  {
    id: "cmcs0ldxa0007jx04q2e85ite",
    name: "Air Jordan 4 (White/Pink)",
    price: "M 1,350.00",
    image: "https://utfs.io/f/9WixNlVtj4Jyc8C1nfDsHhTOMG15xLAeufpF3IZC6g9vWyza",
    badge: "Trendy",
  },
  {
    id: "cmcs0ldy5000bjx04cqor0h42",
    name: "Air Jordan 4 (White/Dark Blue)",
    price: "M 1,350.00",
    image: "https://utfs.io/f/9WixNlVtj4JynOyDR2xLhbI3xYMlHEogprN21uZQB9XU0vDw",
    badge: "Trendy",
  },
  {
    id: "cmcnn08p7001okz4c8qxmxjy0",
    name: "Air Jordan 3",
    price: "M 1,320.00",
    image: "https://utfs.io/f/9WixNlVtj4JyhAw6TxZmpOiTv4rXkRhybJDMEAVeC3gHzWu8",
    badge: "Trendy",
  },
  {
    id: "cmcnn06wb001hkz4cuf950bqh",
    name: "Air Jordan 3 Retro SE Fire Red",
    price: "M 1,350.00",
    image: "https://utfs.io/f/9WixNlVtj4JyQWa77ftYKEanxfu9OGrUM0hoDC3lRNZ7zStI",
    badge: "Trendy",
  },
];

const testimonials = [
  {
    name: "Thabo Mokoena",
    location: "Maseru",
    rating: 5,
    comment:
      "Exceptional quality and comfort. These sneakers exceeded my expectations!",
  },
  {
    name: "Nomsa Lebesa",
    location: "Mafeteng",
    rating: 5,
    comment:
      "Fast delivery and amazing customer service. Will definitely order again.",
  },
  {
    name: "Lerato Mthembu",
    location: "Leribe",
    rating: 5,
    comment: "Perfect fit and style. RIVV truly delivers premium quality.",
  },
];

export default function Home() {
  const { data: session, isPending } = useSession();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUser = async () => {
      if (session?.user?.id) {
        try {
          const userResponse = await getUserById(session.user.id);
          if (userResponse.success && userResponse.data) {
            setUser(userResponse.data);
          }
        } catch (error) {
          console.error("Error fetching user:", error);
        }
      }
      setLoading(false);
    };

    if (!isPending) {
      fetchUser();
    }
  }, [session, isPending]);

  // if (isPending || loading) {
  //   return <SpinnerCircle4 />;
  // }

  return (
    <div className="min-h-screen bg-white">
      {user ? (
        <NavBar
          user={user || (session?.user as User)}
          loading={isPending || loading}
        />
      ) : (
        <NavBarSkeleton loading={isPending || loading} user={null} />
      )}

      {/* Enhanced Hero Section */}
      <section className="relative bg-gradient-to-br from-gray-50 via-blue-50 to-gray-100 py-20 lg:py-32 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              className="space-y-8"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            >
              <div className="space-y-6">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2, duration: 0.6 }}
                >
                  <Badge className="bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 hover:from-blue-200 hover:to-purple-200 border-0 px-4 py-2">
                    <Sparkles className="w-4 h-4 mr-2" />
                    AI-Enhanced Quality
                  </Badge>
                </motion.div>

                <motion.h1
                  className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3, duration: 0.8 }}
                >
                  Purposefully Curated.{" "}
                  <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Unapologetically Premium.
                  </span>
                </motion.h1>

                <motion.p
                  className="text-xl text-gray-600 leading-relaxed"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4, duration: 0.6 }}
                >
                  RIVV Premium Sneakers delivers AI-enhanced quality that speaks for itself.
                  Every pair is carefully selected and enhanced for craftsmanship, comfort, and standout style.
                </motion.p>
              </div>

              <motion.div
                className="flex flex-col sm:flex-row gap-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.6 }}
              >
                <Button
                  asChild
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300"
                  title="Browse our full collection"
                  aria-label="Shop Collection"
                >
                  <Link href="/products">
                    Shop Collection <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="border-2 hover:bg-gray-50 transition-all duration-300"
                  title="Contact our team"
                  aria-label="Contact Us"
                >
                  <Link href="/contact">Contact Us</Link>
                </Button>
              </motion.div>

              {/* Stats */}
              <motion.div
                className="grid grid-cols-3 gap-6 pt-8 border-t border-gray-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.6 }}
              >
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">500+</div>
                  <div className="text-sm text-gray-600">Premium Pairs</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">98%</div>
                  <div className="text-sm text-gray-600">Quality Score</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">24h</div>
                  <div className="text-sm text-gray-600">Fast Delivery</div>
                </div>
              </motion.div>
            </motion.div>

            {/* Enhanced Image Showcase */}
            <motion.div
              className="relative"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
            >
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div className="relative aspect-square rounded-2xl overflow-hidden">
                    <Image
                      src={featuredProducts[0].image}
                      alt="Premium Sneaker"
                      fill
                      className="object-cover hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  <div className="relative aspect-square rounded-2xl overflow-hidden">
                    <Image
                      src={featuredProducts[1].image}
                      alt="Premium Sneaker"
                      fill
                      className="object-cover hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                </div>
                <div className="space-y-4 pt-8">
                  <div className="relative aspect-square rounded-2xl overflow-hidden">
                    <Image
                      src={featuredProducts[2].image}
                      alt="Premium Sneaker"
                      fill
                      className="object-cover hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  <div className="relative aspect-square rounded-2xl overflow-hidden">
                    <Image
                      src={featuredProducts[3].image}
                      alt="Premium Sneaker"
                      fill
                      className="object-cover hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* AI-Enhanced Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              AI-Powered Excellence
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Experience the future of sneaker retail with our AI-enhanced platform
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-6 mb-4 group-hover:shadow-lg transition-all duration-300">
                <Sparkles className="w-8 h-8 text-blue-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">AI Quality Enhancement</h3>
                <p className="text-sm text-gray-600">Every image enhanced with AI for perfect clarity and detail</p>
              </div>
            </motion.div>

            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <div className="bg-gradient-to-br from-green-50 to-blue-50 rounded-2xl p-6 mb-4 group-hover:shadow-lg transition-all duration-300">
                <Shield className="w-8 h-8 text-green-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">Smart Validation</h3>
                <p className="text-sm text-gray-600">AI-powered order validation ensures 100% accuracy</p>
              </div>
            </motion.div>

            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-6 mb-4 group-hover:shadow-lg transition-all duration-300">
                <Zap className="w-8 h-8 text-purple-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">Instant Processing</h3>
                <p className="text-sm text-gray-600">Lightning-fast AI analysis and product enhancement</p>
              </div>
            </motion.div>

            <motion.div
              className="text-center group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <div className="bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl p-6 mb-4 group-hover:shadow-lg transition-all duration-300">
                <TrendingUp className="w-8 h-8 text-orange-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">Quality Metrics</h3>
                <p className="text-sm text-gray-600">Real-time quality scoring for every product</p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Services/Features Checklist Section - Only confirmed features */}
      <section className="py-12 bg-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl lg:text-3xl font-bold text-blue-900 mb-2">Why Shop With Us?</h2>
            <p className="text-lg text-blue-800">What makes RIVV different?</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="flex items-start gap-3">
              <CheckCircle className="h-6 w-6 text-green-600 mt-1" />
              <div>
                <span className="font-semibold text-blue-900">Lay-Buy Payment Plan</span>
                <p className="text-blue-800 text-sm">Pay 60% upfront, the rest over 6 weeks. No interest, no stress!</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <CheckCircle className="h-6 w-6 text-green-600 mt-1" />
              <div>
                <span className="font-semibold text-blue-900">Free Delivery</span>
                <p className="text-blue-800 text-sm">Free in Maseru, and for orders over M3500 in other districts.</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <CheckCircle className="h-6 w-6 text-green-600 mt-1" />
              <div>
                <span className="font-semibold text-blue-900">Secure Payments</span>
                <p className="text-blue-800 text-sm">Pay safely with M-Pesa, EcoCash, or Bank Transfer.</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <CheckCircle className="h-6 w-6 text-green-600 mt-1" />
              <div>
                <span className="font-semibold text-blue-900">Premium Quality</span>
                <p className="text-blue-800 text-sm">Handpicked, authentic sneakers and fashionwear.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Brands Checklist Section - Fashion-forward style */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl lg:text-3xl font-extrabold text-gray-900 mb-2 tracking-tight uppercase">Brands We Carry</h2>
            <p className="text-lg text-gray-700">Shop the world’s most iconic sneaker brands</p>
          </div>
          <div className="flex flex-row gap-6 overflow-x-auto scrollbar-hide py-2 px-1 sm:justify-center">
            {[
              { name: 'Air Jordan', color: 'from-pink-500 to-yellow-500' },
              { name: 'Nike', color: 'from-orange-500 to-yellow-400' },
              { name: 'Adidas', color: 'from-gray-800 to-gray-400' },
              { name: 'Timberland', color: 'from-yellow-700 to-yellow-400' },
              { name: 'New Balance', color: 'from-blue-700 to-blue-300' },
              { name: 'Vans', color: 'from-purple-600 to-pink-400' },
              { name: 'Converse', color: 'from-gray-900 to-gray-500' },
              { name: 'Puma', color: 'from-green-600 to-green-300' },
            ].map((brand) => (
              <div
                key={brand.name}
                className={
                  `flex flex-col items-center min-w-[120px] rounded-xl p-4 shadow-md bg-gradient-to-br ${brand.color} transition-transform hover:-translate-y-1 hover:scale-105 duration-200`}
              >
                <span className="mb-2">
                  <CheckCircle className="h-7 w-7 text-white drop-shadow-lg" />
                </span>
                <span className="font-bold text-lg text-white tracking-wide uppercase drop-shadow-sm">
                  {brand.name}
                </span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900">
              Featured Collection
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Discover our handpicked selection of premium sneakers, each chosen
              for exceptional quality and style.
            </p>
          </div>

          {featuredProducts.length === 0 ? (
            <div className="text-center text-gray-500 py-12">No featured products available at the moment. Please check back soon!</div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredProducts.map((product) => (
                <Card
                  key={product.id}
                  className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2"
                >
                  <CardContent className="p-0">
                    <div className="relative aspect-square overflow-hidden rounded-t-lg">
                      <Image
                        src={product.image}
                        alt={product.name}
                        fill
                        className="object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                      <div className="absolute top-4 left-4">
                        <Badge className="bg-white/90 text-gray-900 hover:bg-white">
                          {product.badge}
                        </Badge>
                      </div>
                      <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button
                          size="sm"
                          variant="secondary"
                          className="rounded-full w-10 h-10 p-0"
                          title="Add to Wishlist"
                          aria-label="Add to Wishlist"
                          onClick={() => alert('Added to wishlist!')}
                        >
                          <Heart className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="p-6 space-y-4">
                      <div>
                        <h3 className="font-semibold text-lg text-gray-900 group-hover:text-blue-600 transition-colors">
                          {product.name}
                        </h3>
                        <div className="flex items-center gap-2 mt-2">
                          <span className="text-2xl font-bold text-gray-900">
                            {product.price}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <Button
                          size="sm"
                          className="bg-blue-600 hover:bg-blue-700"
                          asChild
                          title="View all products"
                          aria-label="View Products"
                          onClick={() => alert('Redirecting to products...')}
                        >
                          <Link href={"/products"}>
                            <BoxesIcon className="h-4 w-4 mr-2" />
                            View Products
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          <div className="text-center mt-12">
            <Button asChild size="lg" variant="outline">
              <Link href="/products">
                View All Products <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Brand Story Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8">
              <div className="space-y-6">
                <h2 className="text-3xl lg:text-4xl font-bold text-gray-900">
                  We're Not Here to Meet Expectations—We're Here to Exceed Them
                </h2>
                <p className="text-lg text-gray-600 leading-relaxed">
                  RIVV Premium Sneakers is a proudly female-founded brand on a
                  mission to deliver quality that speaks for itself. Every pair
                  is carefully selected for its craftsmanship, comfort, and
                  standout style.
                </p>
                <p className="text-lg text-gray-600 leading-relaxed">
                  Step in with confidence. You won't be disappointed.
                </p>
              </div>

              <div className="grid grid-cols-2 gap-8">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-semibold text-gray-900">
                      Premium Quality
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm">
                    Carefully curated for exceptional craftsmanship
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-semibold text-gray-900">
                      Comfort First
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm">
                    Designed for all-day comfort and support
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-semibold text-gray-900">
                      Standout Style
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm">
                    Unique designs that make a statement
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-semibold text-gray-900">
                      Female-Founded
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm">
                    Proudly supporting women in business
                  </p>
                </div>
              </div>

              <Button asChild size="lg">
                <Link href="/contact">
                  Learn More About Us <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>

            <div className="relative">
              <div className="grid grid-cols-2 gap-4">
                <div className="relative aspect-[4/5] rounded-2xl overflow-hidden">
                  <Image
                    src={featuredProducts[4].image}
                    alt="RIVV Premium Sneakers"
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="relative aspect-[4/5] rounded-2xl overflow-hidden mt-8">
                  <Image
                    src={featuredProducts[5].image}
                    alt="RIVV Premium Sneakers"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Customer Testimonials */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900">
              What Our Customers Say
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Don't just take our word for it. Here's what our satisfied
              customers across Lesotho have to say.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card
                key={index}
                className="p-6 hover:shadow-lg transition-shadow"
              >
                <CardContent className="p-0 space-y-4">
                  <div className="flex items-center gap-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="h-5 w-5 fill-yellow-400 text-yellow-400"
                      />
                    ))}
                  </div>
                  <p className="text-gray-700 italic">
                    "{testimonial.comment}"
                  </p>
                  <div className="border-t pt-4">
                    <div className="font-semibold text-gray-900">
                      {testimonial.name}
                    </div>
                    <div className="text-sm text-gray-600">
                      {testimonial.location}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
