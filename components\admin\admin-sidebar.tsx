"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  MessageSquare,
  Tag,
  BarChart3,
  Settings,
  Mail,
  FileText,
  CreditCard,
  CheckCircle,
  Truck,
  Bell,
  Ban,
  Calculator,
  Handshake,
} from "lucide-react";
import { useEffect } from "react";

const menuItems = [
  {
    title: "Dashboard",
    href: "/admin",
    icon: LayoutDashboard,
  },
  {
    title: "Products",
    href: "/admin/products",
    icon: Package,
  },
  {
    title: "Orders",
    href: "/admin/orders",
    icon: ShoppingCart,
  },
  {
    title: "Lay-Buy Orders",
    href: "/admin/lay-buy-orders",
    icon: CreditCard,
  },
  {
    title: "Lay-Buy Payments",
    href: "/admin/lay-buy-payments",
    icon: CheckCircle,
  },
  {
    title: "Lay-Buy Analytics",
    href: "/admin/lay-buy-analytics",
    icon: BarChart3,
  },
  {
    title: "Cancellation Requests",
    href: "/admin/lay-buy-cancellation-requests",
    icon: Ban,
  },
  {
    title: "Users",
    href: "/admin/users",
    icon: Users,
  },
  {
    title: "Sales Partners",
    href: "/admin/partners",
    icon: Handshake,
  },
  {
    title: "Contact Messages",
    href: "/admin/contact-messages",
    icon: MessageSquare,
  },
  {
    title: "Categories",
    href: "/admin/categories",
    icon: Tag,
  },
  {
    title: "Discount Codes",
    href: "/admin/discount-codes",
    icon: Tag,
  },
  {
    title: "Analytics",
    href: "/admin/analytics",
    icon: BarChart3,
  },
  {
    title: "Fees & Expenses",
    href: "/admin/fees-expenses",
    icon: Calculator,
  },
  {
    title: "Fee Settings",
    href: "/admin/fee-settings",
    icon: Settings,
  },
  {
    title: "Settings",
    href: "/admin/settings",
    icon: Settings,
  },
  {
    title: "Notices",
    href: "/admin/notices",
    icon: Bell,
  },
];

export default function AdminSidebar({ open, onClose }: { open?: boolean; onClose?: () => void }) {
  const pathname = usePathname();

  // Prevent background scroll when sidebar is open on mobile
  useEffect(() => {
    if (open) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [open]);

  // Desktop sidebar
  return (
    <>
      {/* Desktop sidebar */}
      <aside className="hidden lg:block w-64 bg-white border-r border-gray-200 min-h-screen">
        <nav className="p-4 space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = pathname === item.href ||
              (item.href !== "/admin" && pathname.startsWith(item.href));

            return (
              <Link
                key={item.href}
                href={item.href}
                title={item.title}
                aria-label={item.title}
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                  isActive
                    ? "bg-blue-100 text-blue-700"
                    : "text-gray-600 hover:bg-gray-100 hover:text-gray-900"
                )}
              >
                <Icon className="h-4 w-4" />
                {item.title}
              </Link>
            );
          })}
        </nav>
      </aside>

      {/* Mobile sidebar drawer */}
      {open && (
        <>
          {/* Overlay */}
          <div
            className="fixed inset-0 z-40 bg-black bg-opacity-40 transition-opacity lg:hidden"
            onClick={onClose}
            aria-label="Close sidebar overlay"
          />
          {/* Drawer */}
          <aside
            className="fixed top-0 left-0 z-50 w-64 h-full max-h-screen overflow-y-auto bg-white border-r border-gray-200 shadow-lg transform transition-transform duration-300 lg:hidden"
            style={{ transform: open ? "translateX(0)" : "translateX(-100%)" }}
            aria-modal="true"
            role="dialog"
          >
            <button
              className="absolute top-4 right-4 p-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
              onClick={onClose}
              aria-label="Close sidebar"
            >
              <svg className="h-6 w-6 text-gray-700" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            <nav className="p-4 space-y-2 mt-10">
              {menuItems.map((item) => {
                const Icon = item.icon;
                const isActive = pathname === item.href ||
                  (item.href !== "/admin" && pathname.startsWith(item.href));

                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    title={item.title}
                    aria-label={item.title}
                    className={cn(
                      "flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                      isActive
                        ? "bg-blue-100 text-blue-700"
                        : "text-gray-600 hover:bg-gray-100 hover:text-gray-900"
                    )}
                    onClick={onClose}
                  >
                    <Icon className="h-4 w-4" />
                    {item.title}
                  </Link>
                );
              })}
            </nav>
          </aside>
        </>
      )}
    </>
  );
}
