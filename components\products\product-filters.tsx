"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Product, ProductFilters as ProductFiltersType } from "@/utils/types";
import { Search, X } from "lucide-react";

interface ProductFiltersProps {
  filters: ProductFiltersType;
  onFiltersChange: (filters: ProductFiltersType) => void;
  products: Product[];
}

export default function ProductFilters({ filters, onFiltersChange, products }: ProductFiltersProps) {
  const [categories, setCategories] = useState<any[]>([]);
  const [priceRange, setPriceRange] = useState([0, 1500]);
  const [searchTerm, setSearchTerm] = useState(filters.search || "");
  const [isClient, setIsClient] = useState(false);

  // Set client-side flag
  useEffect(() => {
    setIsClient(true);
    // Initialize price range from localStorage on client side
    const savedMaxPrice = localStorage.getItem("maxPrice");
    if (savedMaxPrice) {
      setPriceRange([0, Number(savedMaxPrice)]);
    }
  }, []);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch("/api/categories");
        const result = await response.json();
        if (result.success) {
          setCategories(result.data);
        }
      } catch (error) {
        console.error("Error fetching categories:", error);
      }
    };

    fetchCategories();
  }, []);

  // Get available options from products
  const availableBrands = Array.from(new Set(products.map(p => p.brand))).sort();
  const availableSizes = Array.from(new Set(products.flatMap(p => p.sizes))).sort();
  const availableColors = Array.from(new Set(products.flatMap(p => p.colors))).sort();

  // Get price range from products
  useEffect(() => {
    if (products.length > 0 && isClient) {
      const prices = products.map(p => p.discountedPrice || p.price);
      const minPrice = Math.min(0);
      const maxPrice = Math.max(...prices);
      localStorage.setItem("maxPrice", maxPrice.toString());
      setPriceRange([
        filters.minPrice || minPrice,
        filters.maxPrice || maxPrice
      ]);
    }
  }, [products, filters.minPrice, filters.maxPrice, isClient]);

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onFiltersChange({ ...filters, search: searchTerm || undefined });
  };

  const handleCategoryChange = (categoryId: string) => {
    onFiltersChange({ 
      ...filters, 
      categoryId: categoryId === "all" ? undefined : categoryId 
    });
  };

  const handleBrandChange = (brand: string, checked: boolean) => {
    const currentBrands = filters.brand ? [filters.brand] : [];
    let newBrands: string[];
    
    if (checked) {
      newBrands = [...currentBrands, brand];
    } else {
      newBrands = currentBrands.filter(b => b !== brand);
    }
    
    onFiltersChange({ 
      ...filters, 
      brand: newBrands.length > 0 ? newBrands[0] : undefined // For now, support single brand
    });
  };

  const handleSizeChange = (size: string, checked: boolean) => {
    const currentSizes = filters.sizes || [];
    let newSizes: string[];
    
    if (checked) {
      newSizes = [...currentSizes, size];
    } else {
      newSizes = currentSizes.filter(s => s !== size);
    }
    
    onFiltersChange({ 
      ...filters, 
      sizes: newSizes.length > 0 ? newSizes : undefined 
    });
  };

  const handleColorChange = (color: string, checked: boolean) => {
    const currentColors = filters.colors || [];
    let newColors: string[];
    
    if (checked) {
      newColors = [...currentColors, color];
    } else {
      newColors = currentColors.filter(c => c !== color);
    }
    
    onFiltersChange({ 
      ...filters, 
      colors: newColors.length > 0 ? newColors : undefined 
    });
  };

  const handlePriceRangeChange = (values: number[]) => {
    setPriceRange(values);
  };

  const handlePriceRangeCommit = (values: number[]) => {
    onFiltersChange({
      ...filters,
      minPrice: values[0],
      maxPrice: values[1]
    });
  };

  return (
    <div className="space-y-6">
      {/* Filters Label */}
      <div className="mb-2 p-2 bg-blue-100 text-blue-800 rounded text-center font-semibold text-base shadow-sm">
        <span>Filter Products</span>
      </div>
      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Search</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearchSubmit} className="flex gap-2">
            <Input
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1"
            />
            <Button type="submit" size="sm">
              <Search className="h-4 w-4" />
            </Button>
          </form>
          {filters.search && (
            <div className="mt-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setSearchTerm("");
                  onFiltersChange({ ...filters, search: undefined });
                }}
              >
                <X className="h-4 w-4 mr-1" />
                Clear search
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Categories */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Category</CardTitle>
        </CardHeader>
        <CardContent>
          <Select
            value={filters.categoryId || "all"}
            onValueChange={handleCategoryChange}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name} ({category._count.products})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* Price Range */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Price Range</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Slider
            value={priceRange}
            onValueChange={handlePriceRangeChange}
            onValueCommit={handlePriceRangeCommit}
            max={2000}
            min={0}
            step={10}
            className="w-full"
          />
          <div className="flex justify-between text-sm text-gray-600">
            <span>M {priceRange[0]}</span>
            <span>M {priceRange[1]}</span>
          </div>
        </CardContent>
      </Card>

      {/* Brands */}
      {availableBrands.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Brand</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {availableBrands.slice(0, 8).map((brand) => (
              <div key={brand} className="flex items-center space-x-2">
                <Checkbox
                  id={`brand-${brand}`}
                  checked={filters.brand === brand}
                  onCheckedChange={(checked) => handleBrandChange(brand, !!checked)}
                />
                <Label htmlFor={`brand-${brand}`} className="text-sm">
                  {brand}
                </Label>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Sizes */}
      {availableSizes.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Size</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {availableSizes.map((size) => (
              <div key={size} className="flex items-center space-x-2">
                <Checkbox
                  id={`size-${size}`}
                  checked={filters.sizes?.includes(size) || false}
                  onCheckedChange={(checked) => handleSizeChange(size, !!checked)}
                />
                <Label htmlFor={`size-${size}`} className="text-sm">
                  {size}
                </Label>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Colors */}
      {availableColors.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Color</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {availableColors.map((color) => (
              <div key={color} className="flex items-center space-x-2">
                <Checkbox
                  id={`color-${color}`}
                  checked={filters.colors?.includes(color) || false}
                  onCheckedChange={(checked) => handleColorChange(color, !!checked)}
                />
                <Label htmlFor={`color-${color}`} className="text-sm">
                  {color}
                </Label>
              </div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
