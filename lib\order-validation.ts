/**
 * Order Validation System
 * 
 * Validates order calculations before saving to prevent discrepancies
 * Includes: subtotal, discounts, delivery fees, lay-buy calculations
 */

import { prisma } from '@/lib/prisma';
import { Decimal } from '@prisma/client/runtime/library';

export interface OrderValidationResult {
  isValid: boolean;
  originalTotal: number;
  correctedTotal: number;
  discrepancy: number;
  corrections: OrderCorrection[];
  warnings: string[];
}

export interface OrderCorrection {
  type: 'discount_error' | 'delivery_fee' | 'calculation' | 'lay_buy_error' | 'tax_error';
  field: string;
  originalValue: number;
  correctedValue: number;
  description: string;
}

export interface OrderData {
  items: Array<{
    productId: string;
    quantity: number;
    price: number;
  }>;
  discountCode?: string;
  discountAmount?: number;
  deliveryFee?: number;
  isLayBuy?: boolean;
  layBuyUpfrontPercentage?: number;
  subtotal: number;
  total: number;
  customerId?: string;
}

/**
 * Validates an order and returns corrections if needed
 */
export async function validateOrder(orderData: OrderData): Promise<OrderValidationResult> {
  const corrections: OrderCorrection[] = [];
  const warnings: string[] = [];
  
  try {
    // 1. Validate item calculations
    const itemValidation = await validateOrderItems(orderData.items);
    corrections.push(...itemValidation.corrections);
    warnings.push(...itemValidation.warnings);
    
    // 2. Validate discount calculations
    if (orderData.discountCode || orderData.discountAmount) {
      const discountValidation = await validateDiscounts(orderData, itemValidation.calculatedSubtotal);
      corrections.push(...discountValidation.corrections);
      warnings.push(...discountValidation.warnings);
    }
    
    // 3. Validate delivery fees
    const deliveryValidation = await validateDeliveryFees(orderData);
    corrections.push(...deliveryValidation.corrections);
    warnings.push(...deliveryValidation.warnings);
    
    // 4. Validate lay-buy calculations
    if (orderData.isLayBuy) {
      const layBuyValidation = await validateLayBuyCalculations(orderData);
      corrections.push(...layBuyValidation.corrections);
      warnings.push(...layBuyValidation.warnings);
    }
    
    // Calculate corrected total
    const correctedSubtotal = itemValidation.calculatedSubtotal;
    const correctedDiscountAmount = corrections.find(c => c.field === 'discountAmount')?.correctedValue ?? orderData.discountAmount ?? 0;
    const correctedDeliveryFee = corrections.find(c => c.field === 'deliveryFee')?.correctedValue ?? orderData.deliveryFee ?? 0;
    
    const correctedTotal = correctedSubtotal - correctedDiscountAmount + correctedDeliveryFee;
    const discrepancy = Math.abs(orderData.total - correctedTotal);
    
    // Log validation if there are discrepancies
    if (discrepancy > 0.01) { // Allow for small rounding differences
      await logOrderValidation(orderData, {
        originalTotal: orderData.total,
        correctedTotal,
        discrepancy,
        corrections
      });
    }
    
    return {
      isValid: discrepancy <= 0.01,
      originalTotal: orderData.total,
      correctedTotal,
      discrepancy,
      corrections,
      warnings
    };
    
  } catch (error) {
    console.error('Order validation error:', error);
    warnings.push('Validation system error - manual review required');
    
    return {
      isValid: false,
      originalTotal: orderData.total,
      correctedTotal: orderData.total,
      discrepancy: 0,
      corrections,
      warnings
    };
  }
}

/**
 * Validates individual order items and calculates correct subtotal
 */
async function validateOrderItems(items: OrderData['items']) {
  const corrections: OrderCorrection[] = [];
  const warnings: string[] = [];
  let calculatedSubtotal = 0;
  
  // Get current product prices
  const productIds = items.map(item => item.productId);
  const products = await prisma.product.findMany({
    where: { id: { in: productIds } },
    select: { id: true, price: true, discountedPrice: true, isActive: true, stock: true }
  });
  
  const productMap = new Map(products.map(p => [p.id, p]));
  
  for (const item of items) {
    const product = productMap.get(item.productId);
    
    if (!product) {
      warnings.push(`Product ${item.productId} not found`);
      continue;
    }
    
    if (!product.isActive) {
      warnings.push(`Product ${item.productId} is not active`);
    }
    
    if (product.stock < item.quantity) {
      warnings.push(`Insufficient stock for product ${item.productId}`);
    }
    
    // Use discounted price if available, otherwise regular price
    const correctPrice = product.discountedPrice ?? product.price;
    const itemTotal = correctPrice * item.quantity;
    
    if (Math.abs(item.price - correctPrice) > 0.01) {
      corrections.push({
        type: 'calculation',
        field: `item_${item.productId}_price`,
        originalValue: item.price,
        correctedValue: correctPrice,
        description: `Price mismatch for product ${item.productId}`
      });
    }
    
    calculatedSubtotal += itemTotal;
  }
  
  return { corrections, warnings, calculatedSubtotal };
}

/**
 * Validates discount code and amount calculations
 */
async function validateDiscounts(orderData: OrderData, subtotal: number) {
  const corrections: OrderCorrection[] = [];
  const warnings: string[] = [];
  
  if (!orderData.discountCode) {
    return { corrections, warnings };
  }
  
  // Get discount code details
  const discountCode = await prisma.discountCode.findUnique({
    where: { code: orderData.discountCode },
    select: {
      id: true,
      discountType: true,
      discountValue: true,
      isActive: true,
      expiresAt: true,
      usageLimit: true,
      usageCount: true,
      minimumOrderValue: true
    }
  });
  
  if (!discountCode) {
    warnings.push(`Discount code ${orderData.discountCode} not found`);
    return { corrections, warnings };
  }
  
  if (!discountCode.isActive) {
    warnings.push(`Discount code ${orderData.discountCode} is not active`);
  }
  
  if (discountCode.expiresAt && discountCode.expiresAt < new Date()) {
    warnings.push(`Discount code ${orderData.discountCode} has expired`);
  }
  
  if (discountCode.usageLimit && discountCode.usageCount >= discountCode.usageLimit) {
    warnings.push(`Discount code ${orderData.discountCode} usage limit exceeded`);
  }
  
  if (discountCode.minimumOrderValue && subtotal < discountCode.minimumOrderValue) {
    warnings.push(`Order does not meet minimum value for discount code ${orderData.discountCode}`);
  }
  
  // Calculate correct discount amount
  let correctDiscountAmount = 0;
  if (discountCode.discountType === 'PERCENTAGE') {
    correctDiscountAmount = (subtotal * discountCode.discountValue) / 100;
  } else {
    correctDiscountAmount = discountCode.discountValue;
  }
  
  // Ensure discount doesn't exceed subtotal
  correctDiscountAmount = Math.min(correctDiscountAmount, subtotal);
  
  if (Math.abs((orderData.discountAmount ?? 0) - correctDiscountAmount) > 0.01) {
    corrections.push({
      type: 'discount_error',
      field: 'discountAmount',
      originalValue: orderData.discountAmount ?? 0,
      correctedValue: correctDiscountAmount,
      description: `Incorrect discount calculation for code ${orderData.discountCode}`
    });
  }
  
  return { corrections, warnings };
}

/**
 * Validates delivery fee calculations
 */
async function validateDeliveryFees(orderData: OrderData) {
  const corrections: OrderCorrection[] = [];
  const warnings: string[] = [];
  
  // Get delivery fee settings
  const settings = await prisma.feeSettings.findFirst({
    select: {
      deliveryFeePerShoe: true,
      bulkDeliveryThreshold: true,
      bulkDeliveryFee: true
    }
  });
  
  if (!settings) {
    warnings.push('Delivery fee settings not configured');
    return { corrections, warnings };
  }
  
  // Calculate correct delivery fee based on number of items
  const totalQuantity = orderData.items.reduce((sum, item) => sum + item.quantity, 0);
  
  let correctDeliveryFee = 0;
  if (totalQuantity >= (settings.bulkDeliveryThreshold || 5)) {
    correctDeliveryFee = settings.bulkDeliveryFee || 60;
  } else {
    correctDeliveryFee = (settings.deliveryFeePerShoe || 90) * totalQuantity;
  }
  
  if (Math.abs((orderData.deliveryFee ?? 0) - correctDeliveryFee) > 0.01) {
    corrections.push({
      type: 'delivery_fee',
      field: 'deliveryFee',
      originalValue: orderData.deliveryFee ?? 0,
      correctedValue: correctDeliveryFee,
      description: `Incorrect delivery fee calculation`
    });
  }
  
  return { corrections, warnings };
}

/**
 * Validates lay-buy calculation accuracy
 */
async function validateLayBuyCalculations(orderData: OrderData) {
  const corrections: OrderCorrection[] = [];
  const warnings: string[] = [];
  
  if (!orderData.isLayBuy) {
    return { corrections, warnings };
  }
  
  const upfrontPercentage = orderData.layBuyUpfrontPercentage ?? 60;
  
  if (upfrontPercentage < 50 || upfrontPercentage > 100) {
    warnings.push('Invalid lay-buy upfront percentage');
  }
  
  // Additional lay-buy specific validations would go here
  // For example: payment schedule validation, terms compliance, etc.
  
  return { corrections, warnings };
}

/**
 * Logs order validation results for admin review
 */
async function logOrderValidation(orderData: OrderData, validation: Partial<OrderValidationResult>) {
  try {
    await prisma.orderValidation.create({
      data: {
        orderId: 'PENDING', // Will be updated when order is created
        originalTotal: new Decimal(validation.originalTotal ?? 0),
        correctedTotal: new Decimal(validation.correctedTotal ?? 0),
        discrepancy: new Decimal(validation.discrepancy ?? 0),
        correctionType: validation.corrections?.[0]?.type ?? 'calculation',
        details: {
          corrections: validation.corrections,
          orderData: {
            itemCount: orderData.items.length,
            discountCode: orderData.discountCode,
            isLayBuy: orderData.isLayBuy
          }
        }
      }
    });
  } catch (error) {
    console.error('Failed to log order validation:', error);
  }
}

/**
 * Auto-corrects order data based on validation results
 */
export function applyOrderCorrections(orderData: OrderData, validation: OrderValidationResult): OrderData {
  const correctedData = { ...orderData };
  
  for (const correction of validation.corrections) {
    switch (correction.field) {
      case 'discountAmount':
        correctedData.discountAmount = correction.correctedValue;
        break;
      case 'deliveryFee':
        correctedData.deliveryFee = correction.correctedValue;
        break;
      // Add more field corrections as needed
    }
  }
  
  correctedData.total = validation.correctedTotal;
  
  return correctedData;
}
