generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                         String                      @id
  name                       String
  email                      String                      @unique
  emailVerified              Boolean
  image                      String?
  createdAt                  DateTime
  updatedAt                  DateTime
  role                       UserRole                    @default(USER)
  accounts                   Account[]
  cartItems                  CartItem[]
  contactMessages            ContactMessage[]
  orders                     Order[]
  sessions                   Session[]
  noticeRead                 NoticeRead[]
  layBuyOrders               LayBuyOrder[]
  layBuyCancellationRequests LayBuyCancellationRequest[]
  appliedDiscounts           DiscountLog[]
  isPartner                  Boolean                     @default(false)
  salesPartner               SalesPartner?

  @@map("user")
}

model Session {
  id        String   @id
  expiresAt DateTime
  token     String   @unique
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("session")
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("account")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

model Category {
  id          String        @id @default(cuid())
  name        String        @unique
  description String?
  image       String?
  isActive    Boolean       @default(true)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  products    Product[]
  DiscountLog DiscountLog[]

  @@map("category")
}

model Product {
  id                 String            @id @default(cuid())
  name               String
  description        String?
  price              Float
  discountedPrice    Float?
  costPrice          Float? // Base cost price for profit calculation
  shippingFee        Float? // Shipping fee from supplier (default M100)
  lateCollectionFee  Float? // Late collection fee if applicable (default M10)
  totalCost          Float? // Total cost (costPrice + shippingFee + lateCollectionFee)
  brand              String
  categoryId         String
  images             String[]
  sizes              String[]
  colors             String[]
  stock              Int               @default(0)
  isActive           Boolean           @default(true)
  rating             Float             @default(0)
  reviewCount        Int               @default(0)
  costPriceUpdatedAt DateTime? // When cost price was last updated
  costPriceUpdatedBy String? // Admin who updated the cost price
  feesUpdatedAt      DateTime? // When fees were last updated
  feesUpdatedBy      String? // Admin who updated the fees
  // AI Enhancement fields
  aiAnalysis         Json? // Store AI analysis results (tags, quality score, etc.)
  imageSource        String? // "ai_enhanced" | "search_api" | "original" | "manual"
  imageAngles        Json? // Store angle mapping for multi-angle images
  enhancementStatus  String? // "pending" | "processing" | "completed" | "failed"
  lastEnhancedAt     DateTime? // When images were last enhanced
  enhancedBy         String? // Admin who triggered enhancement
  qualityScore       Float? // AI-generated quality score (0-100)
  createdAt          DateTime          @default(now())
  updatedAt          DateTime          @updatedAt
  cartItems          CartItem[]
  orderItems         OrderItem[]
  category           Category          @relation(fields: [categoryId], references: [id])
  reviews            Review[]
  layBuyOrderItems   LayBuyOrderItem[]

  @@map("product")
}

model Review {
  id        String   @id @default(cuid())
  rating    Int
  comment   String?
  productId String
  userId    String
  userName  String
  userEmail String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("review")
}

model CartItem {
  id        String   @id @default(cuid())
  userId    String
  productId String
  quantity  Int      @default(1)
  size      String?
  color     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, productId, size, color])
  @@map("cart_item")
}

model Order {
  id                String           @id @default(cuid())
  userId            String
  orderNumber       String           @unique
  status            OrderStatus      @default(PENDING)
  totalAmount       Float
  totalCostPrice    Float? // Total cost price for profit calculation
  totalProfit       Float? // Total profit (totalAmount - totalCostPrice - discountAmount)
  discountAmount    Float            @default(0)
  discountCodeId    String?
  shippingAddress   String
  phoneNumber       String
  notes             String?
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  adminNotes        String?
  discountCode      DiscountCode?    @relation(fields: [discountCodeId], references: [id])
  user              User             @relation(fields: [userId], references: [id])
  orderItems        OrderItem[]
  trackingNumber    String? // Delivery tracking number
  trackingUrl       String? // Delivery tracking URL
  delvaNotified     Boolean          @default(false) // Whether Delva has been notified
  delvaNotifiedAt   DateTime? // When Delva was notified
  deliveryFee       Float? // Delivery fee owed to Delva (M90 or M60 for bulk)
  deliveryFeePaid   Boolean          @default(false) // Whether delivery fee has been paid to Delva
  deliveryFeePaidAt DateTime? // When delivery fee was paid
  deliveredAt       DateTime? // When order was marked as delivered
  isBulkDelivery    Boolean          @default(false) // Whether this was part of a bulk delivery (5+)
  paymentProof      PaymentProof?
  deliveryRecords   DeliveryRecord[]

  // Location tracking for delivery
  customerLatitude  Float? // Customer's latitude for delivery
  customerLongitude Float? // Customer's longitude for delivery
  locationUpdatedAt DateTime? // When location was last updated
  locationEnabled   Boolean   @default(false) // Whether customer has enabled location sharing

  @@map("order")
}

model OrderItem {
  id                String   @id @default(cuid())
  orderId           String
  productId         String
  quantity          Int
  price             Float // Sale price at time of order
  costPrice         Float? // Base cost price at time of order
  shippingFee       Float? // Shipping fee at time of order
  lateCollectionFee Float? // Late collection fee at time of order
  totalCost         Float? // Total cost (costPrice + shippingFee + lateCollectionFee)
  profit            Float? // Calculated profit (price - totalCost) * quantity
  size              String?
  color             String?
  createdAt         DateTime @default(now())
  order             Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product           Product  @relation(fields: [productId], references: [id])

  @@map("order_item")
}

enum OrderStatus {
  PENDING
  PAID
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
}

// Discount Code Model
model DiscountCode {
  id           String        @id @default(cuid())
  code         String        @unique
  description  String?
  type         DiscountType
  value        Float
  minAmount    Float?
  maxUses      Int?
  usedCount    Int           @default(0)
  isActive     Boolean       @default(true)
  validFrom    DateTime      @default(now())
  validUntil   DateTime?
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  orders       Order[]
  discountLogs DiscountLog[]

  @@map("discount_code")
}

model PaymentProof {
  id         String        @id @default(cuid())
  orderId    String        @unique
  imageUrl   String
  status     PaymentStatus @default(PENDING)
  notes      String?
  verifiedBy String?
  verifiedAt DateTime?
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt
  order      Order         @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("payment_proof")
}

model Notice {
  id        String     @id @default(cuid())
  title     String
  content   String
  type      NoticeType @default(INFO)
  isActive  Boolean    @default(true)
  priority  Int        @default(0)
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  noticeRead NoticeRead[]

  @@map("notice")
}

model Testimonial {
  id        String   @id @default(cuid())
  name      String
  content   String
  rating    Int
  image     String?
  position  String?
  isActive  Boolean  @default(true)
  priority  Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("testimonial")
}

model ContactMessage {
  id         String        @id @default(cuid())
  userId     String?
  name       String
  email      String
  subject    String
  message    String
  status     MessageStatus @default(UNREAD)
  adminNotes String?
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt
  readAt     DateTime?
  user       User?         @relation(fields: [userId], references: [id])

  @@map("contact_message")
}

enum MessageStatus {
  UNREAD
  READ
  REPLIED
  RESOLVED
}

model DiscountLog {
  id               String        @id @default(cuid())
  type             DiscountType
  value            Float
  category         Category?     @relation(fields: [categoryId], references: [id])
  categoryId       String?
  productsAffected Int
  appliedBy        User          @relation(fields: [appliedById], references: [id])
  appliedById      String
  createdAt        DateTime      @default(now())
  DiscountCode     DiscountCode? @relation(fields: [discountCodeId], references: [id])
  discountCodeId   String?

  @@map("discount_log")
}

model Settings {
  id                       String   @id @default(cuid())
  primaryColor             String   @default("#3b82f6")
  defaultShippingFee       Float    @default(100)
  defaultLateCollectionFee Float    @default(10)
  defaultDeliveryFee       Float    @default(90)
  defaultBulkDeliveryFee   Float    @default(60)
  bulkDeliveryThreshold    Int      @default(5)
  createdAt                DateTime @default(now())
  updatedAt                DateTime @updatedAt
}

model NoticeRead {
  id       String   @id @default(cuid())
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId   String
  notice   Notice   @relation(fields: [noticeId], references: [id], onDelete: Cascade)
  noticeId String
  readAt   DateTime @default(now())

  @@unique([userId, noticeId])
  @@map("notice_read")
}

enum UserRole {
  USER
  ADMIN
}

enum DiscountType {
  PERCENTAGE
  FIXED_AMOUNT
}

enum PaymentStatus {
  PENDING
  VERIFIED
  REJECTED
}

enum NoticeType {
  INFO
  WARNING
  SUCCESS
  ERROR
}

// Lay-Buy System Models
model LayBuyOrder {
  id              String       @id @default(cuid())
  user            User         @relation(fields: [userId], references: [id])
  userId          String
  orderNumber     String       @unique
  status          LayBuyStatus @default(ACTIVE)
  totalAmount     Float // Full product price
  upfrontAmount   Float? // 60% paid upfront (optional for migration)
  remainingAmount Float? // 40% remaining (optional for migration)
  amountPaid      Float?       @default(0) // Total amount paid so far (optional for migration)
  dueDate         DateTime? // 6 weeks from creation (optional for migration)
  gracePeriodEnd  DateTime? // 1 week after due date (optional for migration)
  shippingAddress String
  phoneNumber     String
  notes           String?
  adminNotes      String?
  cancelledAt     DateTime?
  completedAt     DateTime?
  forfeitedAt     DateTime?
  refundAmount    Float? // Amount refunded if cancelled early
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt

  // Relations
  orderItems           LayBuyOrderItem[]
  payments             LayBuyPayment[]
  reminders            LayBuyReminder[]
  cancellationRequests LayBuyCancellationRequest[]

  @@map("lay_buy_order")
}

model LayBuyOrderItem {
  id            String      @id @default(cuid())
  layBuyOrder   LayBuyOrder @relation(fields: [layBuyOrderId], references: [id], onDelete: Cascade)
  layBuyOrderId String
  product       Product     @relation(fields: [productId], references: [id])
  productId     String
  quantity      Int
  price         Float? // Price at time of order (optional for migration)
  size          String?
  color         String?
  createdAt     DateTime?   @default(now())

  @@map("lay_buy_order_item")
}

model LayBuyPayment {
  id            String             @id @default(cuid())
  layBuyOrder   LayBuyOrder        @relation(fields: [layBuyOrderId], references: [id], onDelete: Cascade)
  layBuyOrderId String
  amount        Float?
  paymentType   LayBuyPaymentType?
  paymentMethod String? // e.g., "M-Pesa", "Bank Transfer"
  paymentProof  String? // URL to payment proof image
  status        PaymentStatus?     @default(PENDING)
  notes         String?
  verifiedBy    String? // Admin who verified
  verifiedAt    DateTime?
  createdAt     DateTime?          @default(now())
  updatedAt     DateTime?          @updatedAt

  @@map("lay_buy_payment")
}

model LayBuyReminder {
  id            String        @id @default(cuid())
  layBuyOrder   LayBuyOrder   @relation(fields: [layBuyOrderId], references: [id], onDelete: Cascade)
  layBuyOrderId String
  weekNumber    Int? // 1-7 (week 7 is grace period)
  sentAt        DateTime?     @default(now())
  emailSent     Boolean?      @default(false)
  smsSent       Boolean?      @default(false)
  reminderType  ReminderType?
  createdAt     DateTime?     @default(now())

  @@map("lay_buy_reminder")
}

model LayBuyCancellationRequest {
  id            String                     @id @default(cuid())
  layBuyOrder   LayBuyOrder                @relation(fields: [layBuyOrderId], references: [id], onDelete: Cascade)
  layBuyOrderId String
  requestedBy   User                       @relation(fields: [requestedById], references: [id])
  requestedById String
  reason        String? // Customer's reason for cancellation
  status        CancellationRequestStatus? @default(PENDING)
  refundAmount  Float? // Calculated refund amount
  adminNotes    String? // Admin's notes on the request
  processedBy   String? // Admin who processed the request
  processedAt   DateTime? // When the request was processed
  createdAt     DateTime?                  @default(now())
  updatedAt     DateTime?                  @updatedAt

  @@map("lay_buy_cancellation_request")
}

// Lay-Buy Enums
enum LayBuyStatus {
  ACTIVE // Customer is making payments
  COMPLETED // Fully paid
  CANCELLED // Cancelled by customer or admin
  FORFEITED // Not paid within grace period
  REFUNDED // Refund processed
}

enum LayBuyPaymentType {
  UPFRONT // Initial 60% payment
  INSTALLMENT // Subsequent payments
  COMPLETION // Final payment to complete order
}

enum ReminderType {
  WEEKLY // Regular weekly reminder
  URGENT // Week 6 urgent reminder
  GRACE_PERIOD // Week 7 grace period reminder
  FINAL_NOTICE // Final notice before forfeiture
}

enum CancellationRequestStatus {
  PENDING // Request submitted, awaiting admin review
  APPROVED // Request approved, refund processing
  REJECTED // Request rejected by admin
  PROCESSED // Refund completed
}

// Delivery System Models
model DeliveryRecord {
  id                    String          @id @default(cuid())
  order                 Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)
  orderId               String
  partner               DeliveryPartner @default(DELVA)
  status                DeliveryStatus  @default(SCHEDULED)
  trackingNumber        String?         @unique
  customerName          String
  customerPhone         String
  customerEmail         String
  deliveryAddress       String
  items                 Json // Array of items for delivery
  totalAmount           Float
  deliveryFee           Float
  isBulkDelivery        Boolean         @default(false)
  preferredDeliveryDate DateTime?
  specialInstructions   String?
  estimatedDeliveryDate DateTime?
  scheduledAt           DateTime        @default(now())
  pickedUpAt            DateTime?
  deliveredAt           DateTime?
  lastUpdate            DateTime?
  notes                 String?
  createdAt             DateTime        @default(now())
  updatedAt             DateTime        @updatedAt

  @@map("delivery_record")
}

enum DeliveryPartner {
  DELVA
  SELF_DELIVERY
}

enum DeliveryStatus {
  SCHEDULED
  CONFIRMED
  PICKED_UP
  IN_TRANSIT
  OUT_FOR_DELIVERY
  DELIVERED
  FAILED
  RETURNED
  CANCELLED
}

model SalesPartner {
  id               String          @id @default(cuid())
  name             String
  surname          String
  email            String          @unique
  cellNumber       String
  otherCellNumber  String?
  referralCode     String          @unique
  discountAmount   Float           @default(0)
  discountCode     String          @unique
  isActive         Boolean         @default(true)
  commissionEarned Float           @default(0)
  bonusPaid        Float           @default(0)
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @updatedAt
  userId           String?         @unique
  user             User?           @relation(fields: [userId], references: [id], onDelete: Cascade)
  referralOrders   ReferralOrder[]
}

model ReferralOrder {
  id           String       @id @default(cuid())
  orderId      String
  partnerId    String
  customerName String
  orderValue   Float
  commission   Float
  createdAt    DateTime     @default(now())
  partner      SalesPartner @relation(fields: [partnerId], references: [id])
}

model OrderValidation {
  id              String   @id @default(cuid())
  orderId         String
  originalTotal   Decimal
  correctedTotal  Decimal
  discrepancy     Decimal
  correctionType  String   // "discount_error" | "delivery_fee" | "calculation" | "lay_buy_error"
  details         Json?    // Additional validation details
  correctedBy     String?  // Admin who made the correction
  createdAt       DateTime @default(now())

  @@map("order_validation")
}

model ImageEnhancementLog {
  id              String   @id @default(cuid())
  productId       String
  originalImageUrl String
  enhancedImageUrl String?
  enhancementType String   // "quality" | "background" | "multi_angle" | "branding"
  status          String   // "pending" | "processing" | "completed" | "failed"
  errorMessage    String?
  processingTime  Int?     // Processing time in milliseconds
  qualityScore    Float?   // Quality improvement score
  triggeredBy     String?  // Admin who triggered enhancement
  createdAt       DateTime @default(now())
  completedAt     DateTime?

  @@map("image_enhancement_log")
}
