#!/usr/bin/env tsx

/**
 * Bulk Enhancement Script for Existing Products
 * 
 * This script processes all existing products in batches to:
 * - Re-analyze product images with AI
 * - Enhance image quality and apply branding
 * - Search for multi-angle images
 * - Update database with new metadata
 * 
 * Usage: pnpm run enhance-products [--batch-size=10] [--dry-run] [--product-id=specific-id]
 */

import { PrismaClient } from '@prisma/client';
import { processProductImages, generateImageTags, detectImageDefects } from '../utils/image-ai-utils';
import { Resend } from 'resend';

const prisma = new PrismaClient();
const resend = new Resend(process.env.RESEND_API_KEY);

interface EnhancementStats {
  total: number;
  processed: number;
  enhanced: number;
  failed: number;
  skipped: number;
  startTime: Date;
  errors: Array<{ productId: string; error: string }>;
}

interface ScriptOptions {
  batchSize: number;
  dryRun: boolean;
  productId?: string;
  skipExisting: boolean;
}

async function parseArguments(): Promise<ScriptOptions> {
  const args = process.argv.slice(2);
  const options: ScriptOptions = {
    batchSize: parseInt(process.env.AI_BATCH_SIZE || '10'),
    dryRun: false,
    skipExisting: true
  };

  for (const arg of args) {
    if (arg.startsWith('--batch-size=')) {
      options.batchSize = parseInt(arg.split('=')[1]);
    } else if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg.startsWith('--product-id=')) {
      options.productId = arg.split('=')[1];
    } else if (arg === '--force') {
      options.skipExisting = false;
    }
  }

  return options;
}

async function getProductsToProcess(options: ScriptOptions) {
  const where: any = {
    isActive: true,
    images: { isEmpty: false }
  };

  if (options.productId) {
    where.id = options.productId;
  } else if (options.skipExisting) {
    where.OR = [
      { enhancementStatus: null },
      { enhancementStatus: 'failed' },
      { lastEnhancedAt: { lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } } // Older than 7 days
    ];
  }

  return await prisma.product.findMany({
    where,
    select: {
      id: true,
      name: true,
      brand: true,
      images: true,
      enhancementStatus: true,
      lastEnhancedAt: true,
      category: { select: { name: true } }
    },
    orderBy: { createdAt: 'desc' }
  });
}

async function enhanceProduct(product: any, stats: EnhancementStats): Promise<boolean> {
  const startTime = Date.now();
  
  try {
    console.log(`🔄 Processing: ${product.name} (${product.brand})`);
    
    // Update status to processing
    await prisma.product.update({
      where: { id: product.id },
      data: { enhancementStatus: 'processing' }
    });

    // Create enhancement log entry
    const logEntry = await prisma.imageEnhancementLog.create({
      data: {
        productId: product.id,
        originalImageUrl: product.images[0] || '',
        enhancementType: 'multi_angle',
        status: 'processing',
        triggeredBy: 'bulk_script'
      }
    });

    const originalImageUrl = product.images[0];
    if (!originalImageUrl) {
      throw new Error('No images found for product');
    }

    // Process images with AI enhancement
    const enhancementResult = await processProductImages({
      productName: product.name,
      brand: product.brand,
      originalImageUrl
    });

    // Generate additional AI analysis
    const [tags, defects] = await Promise.allSettled([
      generateImageTags(originalImageUrl),
      detectImageDefects(originalImageUrl)
    ]);

    const aiAnalysis = {
      tags: tags.status === 'fulfilled' ? tags.value : [],
      defects: defects.status === 'fulfilled' ? defects.value : null,
      enhancementResult,
      processedAt: new Date().toISOString(),
      processingTime: Date.now() - startTime
    };

    // Calculate quality score based on enhancement results
    const qualityScore = calculateQualityScore(enhancementResult, aiAnalysis);

    // Update product with enhancement results
    await prisma.product.update({
      where: { id: product.id },
      data: {
        images: enhancementResult.images.map((img: any) => img.url),
        imageSource: enhancementResult.images_source,
        imageAngles: enhancementResult.images.reduce((acc: any, img: any) => {
          acc[img.angle] = img.url;
          return acc;
        }, {}),
        aiAnalysis,
        enhancementStatus: 'completed',
        lastEnhancedAt: new Date(),
        enhancedBy: 'bulk_script',
        qualityScore
      }
    });

    // Update enhancement log
    await prisma.imageEnhancementLog.update({
      where: { id: logEntry.id },
      data: {
        enhancedImageUrl: enhancementResult.images[0]?.url,
        status: 'completed',
        processingTime: Date.now() - startTime,
        qualityScore,
        completedAt: new Date()
      }
    });

    console.log(`✅ Enhanced: ${product.name} (Quality: ${qualityScore}%)`);
    stats.enhanced++;
    return true;

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Failed: ${product.name} - ${errorMessage}`);
    
    stats.errors.push({
      productId: product.id,
      error: errorMessage
    });

    // Update product status to failed
    await prisma.product.update({
      where: { id: product.id },
      data: { 
        enhancementStatus: 'failed',
        lastEnhancedAt: new Date()
      }
    }).catch(console.error);

    stats.failed++;
    return false;
  }
}

function calculateQualityScore(enhancementResult: any, aiAnalysis: any): number {
  let score = 50; // Base score
  
  // Bonus for multiple angles
  if (enhancementResult.images.length > 1) {
    score += 20;
  }
  
  // Bonus for enhanced images vs original
  if (enhancementResult.images_source === 'search_api') {
    score += 25;
  } else if (enhancementResult.images_source === 'enhanced_original') {
    score += 15;
  }
  
  // Bonus for no defects
  if (aiAnalysis.defects && !aiAnalysis.defects.hasDefects) {
    score += 10;
  }
  
  // Bonus for good tags
  if (aiAnalysis.tags && aiAnalysis.tags.length > 3) {
    score += 5;
  }
  
  return Math.min(100, Math.max(0, score));
}

async function processBatch(products: any[], batchIndex: number, options: ScriptOptions, stats: EnhancementStats) {
  console.log(`\n📦 Processing batch ${batchIndex + 1} (${products.length} products)`);
  
  if (options.dryRun) {
    console.log('🔍 DRY RUN - No changes will be made');
    products.forEach(product => {
      console.log(`  - ${product.name} (${product.brand})`);
    });
    stats.processed += products.length;
    return;
  }

  const concurrency = parseInt(process.env.BULK_PROCESSING_CONCURRENCY || '3');
  
  for (let i = 0; i < products.length; i += concurrency) {
    const batch = products.slice(i, i + concurrency);
    
    await Promise.allSettled(
      batch.map(product => enhanceProduct(product, stats))
    );
    
    stats.processed += batch.length;
    
    // Progress update
    const progress = ((stats.processed / stats.total) * 100).toFixed(1);
    console.log(`📊 Progress: ${stats.processed}/${stats.total} (${progress}%)`);
    
    // Small delay to prevent API rate limiting
    if (i + concurrency < products.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}

async function sendCompletionNotification(stats: EnhancementStats, options: ScriptOptions) {
  const duration = Date.now() - stats.startTime.getTime();
  const durationMinutes = Math.round(duration / 60000);
  
  const emailContent = `
    <h2>🎨 Product Enhancement Script Completed</h2>
    <p><strong>Execution Summary:</strong></p>
    <ul>
      <li>Total Products: ${stats.total}</li>
      <li>Successfully Enhanced: ${stats.enhanced}</li>
      <li>Failed: ${stats.failed}</li>
      <li>Skipped: ${stats.skipped}</li>
      <li>Duration: ${durationMinutes} minutes</li>
      <li>Dry Run: ${options.dryRun ? 'Yes' : 'No'}</li>
    </ul>
    
    ${stats.errors.length > 0 ? `
      <h3>❌ Errors (${stats.errors.length}):</h3>
      <ul>
        ${stats.errors.slice(0, 10).map(err => `<li>${err.productId}: ${err.error}</li>`).join('')}
        ${stats.errors.length > 10 ? `<li>... and ${stats.errors.length - 10} more</li>` : ''}
      </ul>
    ` : ''}
  `;

  try {
    await resend.emails.send({
      from: 'Rivv System <<EMAIL>>',
      to: [process.env.ADMIN_EMAIL!],
      subject: `🎨 Product Enhancement Complete - ${stats.enhanced}/${stats.total} Enhanced`,
      html: emailContent
    });
  } catch (error) {
    console.error('Failed to send notification email:', error);
  }
}

async function main() {
  console.log('🚀 Starting Rivv Product Enhancement Script\n');
  
  const options = await parseArguments();
  console.log('⚙️  Configuration:', options);
  
  const stats: EnhancementStats = {
    total: 0,
    processed: 0,
    enhanced: 0,
    failed: 0,
    skipped: 0,
    startTime: new Date(),
    errors: []
  };

  try {
    // Get products to process
    const allProducts = await getProductsToProcess(options);
    stats.total = allProducts.length;
    
    if (stats.total === 0) {
      console.log('✨ No products need enhancement. All done!');
      return;
    }
    
    console.log(`📋 Found ${stats.total} products to process\n`);
    
    // Process in batches
    for (let i = 0; i < allProducts.length; i += options.batchSize) {
      const batch = allProducts.slice(i, i + options.batchSize);
      const batchIndex = Math.floor(i / options.batchSize);
      
      await processBatch(batch, batchIndex, options, stats);
      
      // Small delay between batches
      if (i + options.batchSize < allProducts.length) {
        console.log('⏳ Waiting 2 seconds before next batch...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    // Final summary
    console.log('\n🎉 Enhancement Complete!');
    console.log(`📊 Final Stats:`);
    console.log(`   Enhanced: ${stats.enhanced}`);
    console.log(`   Failed: ${stats.failed}`);
    console.log(`   Total: ${stats.total}`);
    
    // Send notification
    await sendCompletionNotification(stats, options);
    
  } catch (error) {
    console.error('💥 Script failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

export { main as enhanceExistingProducts };
